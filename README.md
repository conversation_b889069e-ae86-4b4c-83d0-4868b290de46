# Arien AI CLI

A sophisticated AI-powered CLI terminal system with LLM integration and autonomous tool execution capabilities.

## Features

### 🤖 AI Provider Integration
- **DeepSeek API**: Support for `deepseek-chat` and `deepseek-reasoner` models
- **Ollama Local**: Run models locally with full privacy and control
- **Dynamic Switching**: Change providers and models on-the-fly with `/provider` and `/model`
- **Function Calling**: Native support for tool execution through AI function calls

### 🛠️ Autonomous Tool System
- **Intelligent Decision Making**: AI autonomously decides when and how to use tools
- **Comprehensive Tool Suite**: File operations, web search, system commands, and more
- **Never Give Up Logic**: Automatic retry with alternative approaches when operations fail
- **Multi-Tool Workflows**: Chain multiple tools together for complex tasks
- **Safety First**: Built-in validation and security checks for all operations

### 💬 Advanced Chat Interface
- **Real-time Streaming**: Live AI responses with smooth character-by-character display
- **Interruption Support**: Press ESC twice to stop ongoing AI responses
- **Slash Commands**: Quick access to system functions (`/help`, `/model`, `/provider`, etc.)
- **Command Palette**: Interactive dropdown with arrow key navigation
- **Beautiful UI**: Animated loading indicators, colored output, and professional styling

### 🔄 Reliability & Performance
- **Intelligent Retry Logic**: Exponential backoff and alternative strategy exploration
- **Error Recovery**: Graceful handling of network issues, API limits, and system errors
- **Self-Learning System**: Prompt evolution based on performance metrics and user feedback
- **Health Monitoring**: Built-in system health checks and diagnostics

### 🔒 Security & Safety
- **Command Validation**: Dangerous operations are blocked with clear explanations
- **Path Sanitization**: File operations are validated for safety
- **Secure Storage**: API keys and sensitive data are stored securely
- **Permission Checks**: System operations respect user permissions and boundaries

## Quick Start

### Installation

#### Universal Installer (Recommended)

**Linux/macOS/WSL:**
```bash
# Download and run the universal installer
curl -fsSL https://raw.githubusercontent.com/arien-ai/cli/main/install-arien.sh | bash

# Or download and run manually
wget https://raw.githubusercontent.com/arien-ai/cli/main/install-arien.sh
chmod +x install-arien.sh
./install-arien.sh install
```

**Windows PowerShell:**
```powershell
# Download and run the PowerShell installer
iwr -useb https://raw.githubusercontent.com/arien-ai/cli/main/install-arien.ps1 | iex

# Or download and run manually
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/arien-ai/cli/main/install-arien.ps1" -OutFile "install-arien.ps1"
.\install-arien.ps1 install
```

#### Manual Installation
```bash
# Clone and install manually
git clone https://github.com/arien-ai/cli.git
cd cli
npm install
npm run build
npm run install:global
```

#### Update/Uninstall
```bash
# Update existing installation
./install-arien.sh update

# Uninstall completely
./install-arien.sh uninstall
```

### Setup

```bash
# Run the interactive setup wizard
arien setup

# Or start directly (will prompt for setup if needed)
arien
```

## Usage

### Basic Commands

```bash
# Start interactive chat
arien

# Show help
arien --help

# List available tools
arien tools --list

# Show tool documentation
arien tools --docs bash

# Manage providers
arien providers --list
arien providers --add deepseek
arien providers --test ollama
```

### Slash Commands

While in chat mode, use these commands:

- `/help` - Show available commands
- `/model` - Switch between models
- `/provider` - Change AI provider
- `/config` - View/modify configuration
- `/tools` - List available tools
- `/history` - Show conversation history
- `/clear` - Clear screen
- `/exit` - Exit application

### Interruption

- **Single Ctrl+C**: Exit application
- **Double ESC**: Interrupt AI response while streaming

## AI Providers

### DeepSeek

```bash
# Setup DeepSeek provider
arien providers --add deepseek

# Required: API key from https://platform.deepseek.com/
# Models: deepseek-chat, deepseek-reasoner
```

### Ollama (Local)

```bash
# Setup Ollama provider
arien providers --add ollama

# Requires: Ollama running locally
# Install Ollama: https://ollama.ai/
# Pull models: ollama pull llama2
```

## Available Tools

The AI can autonomously use these tools based on your requests:

### System Tools
- **bash**: Execute shell commands safely
- **grep**: Search file contents with patterns
- **glob**: Find files by name patterns

### File Tools
- **write**: Create or update files
- **edit**: Precise file modifications (replace, insert, delete)

### Web Tools
- **web**: Search the internet and fetch web content

Each tool includes comprehensive documentation, safety features, and error handling.

## Configuration

Configuration is stored in `~/.config/arien-ai-cli/` and includes:

- Provider settings (API keys, models, endpoints)
- User preferences (theme, auto-save, notifications)
- Prompt templates and learning data

```bash
# View current configuration
arien config --list

# Reset to defaults
arien config --reset
```

## Development

### Prerequisites

- Node.js 22.0.0 or later
- TypeScript 5.8+
- Git

### Setup Development Environment

```bash
git clone https://github.com/arien-ai/cli.git
cd cli
npm install
npm run dev
```

### Available Scripts

```bash
npm run build          # Build for production
npm run dev            # Development mode with hot reload
npm run test           # Run tests
npm run test:coverage  # Run tests with coverage
npm run lint           # Lint code
npm run format         # Format code
npm run typecheck      # Type checking
```

### Project Structure

```
src/
├── core/              # Core system components
│   ├── ai/           # AI provider implementations
│   ├── cli/          # CLI interface components
│   ├── tools/        # Function tools
│   └── config/       # Configuration management
├── components/        # Reusable UI components
├── utils/            # Utility functions
├── types/            # TypeScript definitions
└── index.ts          # Main entry point
```

## Architecture

### AI Integration
- Provider-agnostic design supports multiple LLM APIs
- Function calling enables autonomous tool execution
- Streaming responses with real-time display
- Context management and conversation history

### Tool System
- Modular tool architecture with easy extensibility
- Comprehensive parameter validation and error handling
- Detailed documentation and usage examples
- Security features and safe execution

### Error Handling
- Intelligent retry logic with exponential backoff
- Categorized error types with appropriate responses
- Graceful degradation and alternative approaches
- User-friendly error messages and suggestions

## Security

- Path validation prevents directory traversal
- Command filtering blocks dangerous operations
- API key secure storage and handling
- Sandboxed tool execution environment

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) for details.

## Support

- 📖 Documentation: [docs/](docs/)
- 🐛 Issues: [GitHub Issues](https://github.com/arien-ai/cli/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/arien-ai/cli/discussions)

## Roadmap

- [ ] Additional AI providers (OpenAI, Anthropic, etc.)
- [ ] Plugin system for custom tools
- [ ] Web interface companion
- [ ] Team collaboration features
- [ ] Advanced prompt engineering tools
- [ ] Integration with popular development tools

---

Built with ❤️ by the Arien AI team
